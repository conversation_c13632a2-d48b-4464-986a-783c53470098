using Binit.Framework;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Interfaces.DAL;
using DAL.Interfaces;
using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.SampleCageReportDTOs;
using Domain.Logic.DTOs.SampleCageReportDTOs;
using Domain.Logic.Interfaces;
using Domain.Logic.Validations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.SampleCageReportBusinessLogic;

namespace Domain.Logic.BusinessLogic
{
    public class SampleCageReportBusinessLogic : ISampleCageReportBusinessLogic
    {
        private readonly ICapacityUnitBusinessLogic capacityUnitBusinessLogic;
        private readonly IHenBatchService henBatchService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IService<SampleCage> sampleCageService;
        private readonly ISampleCageReportService sampleCageReportService;
        private readonly IServiceTenantDependent<SampleCageMeasurement> sampleCageMeasurementService;
        private readonly IUnitOfWork unitOfWork;
        private readonly IFarmService farmService;
        private readonly IUpdateHenBatchPerformanceBusinessLogic updateHenBatchPerformanceBusinessLogic;
        private readonly IEggWeightReportService eggWeightReportService;
        private readonly IHenReportService henReportService;


        public SampleCageReportBusinessLogic(
            ICapacityUnitBusinessLogic capacityUnitBusinessLogic,
            IHenBatchService henBatchService,
            IHenBatchPerformanceService henBatchPerformanceService,
            IStringLocalizer<SharedResources> localizer,
            IService<SampleCage> sampleCageService,
            ISampleCageReportService sampleCageReportService,
            IServiceTenantDependent<SampleCageMeasurement> sampleCageMeasurementService,
            IUnitOfWork unitOfWork,
            IFarmService farmService,
            IUpdateHenBatchPerformanceBusinessLogic updateHenBatchPerformanceBusinessLogic,
            IEggWeightReportService eggWeightReportService,
            IHenReportService henReportService
            )
        {
            this.capacityUnitBusinessLogic = capacityUnitBusinessLogic;
            this.henBatchService = henBatchService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.localizer = localizer;
            this.sampleCageService = sampleCageService;
            this.sampleCageReportService = sampleCageReportService;
            this.sampleCageMeasurementService = sampleCageMeasurementService;
            this.unitOfWork = unitOfWork;
            this.farmService = farmService;
            this.updateHenBatchPerformanceBusinessLogic = updateHenBatchPerformanceBusinessLogic;
            this.eggWeightReportService = eggWeightReportService;
            this.henReportService = henReportService;
        }

        /// <summary>
        /// fetch a hen batch performance for the same day and hen batch,
        /// add the values of the report to it.
        /// </summary>
        public async Task CreateAndAddToHenBatchPerformanceAsync(SampleCageReport sampleCageReport)
        {
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                await CreateSampleCageReport(sampleCageReport);
            });
        }

        /// <summary>
        /// creates warehouse reports and add them to the hen batch performance
        /// </summary>
        public async Task CreateReports(List<SampleCageReport> reports)
        {
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                foreach (SampleCageReport sampleCageReport in reports)
                    await CreateSampleCageReport(sampleCageReport);
            });
        }

        /// <summary>
        /// Updates henWarehouse data in all sampleCageReports belonging to a report's warehouse on that date
        /// </summary>
        public async Task UpdateHenWarehouseReports(SampleCageReport report)
        {
            Guid warehouseId = henBatchService.GetAll().Where(hb => hb.Id == report.HenBatchId).Select(hb => hb.Line.WarehouseId).FirstOrDefault();
            IQueryable<SampleCageReport> warehouseReports = sampleCageReportService.GetAll()
                   .Where(scr => scr.Date.Date == report.Date.Date && (scr.HenBatch.Line.WarehouseId == warehouseId));
            List<SampleCageReport> warehouseReportsList = warehouseReports.ToList();

            int week = henBatchService.GetCurrentWeekNumberForDate(report.HenBatchId, report.Date);
            HenBatchPerformance performance = await henBatchPerformanceService.GetAll().FirstOrDefaultAsync(hbp => hbp.HenBatchId == report.HenBatchId && hbp.WeekNumber == week);
            if (performance is null)
                throw new ValidationException(null, new BusinessValidationResult<SampleCageReport>(localizer[Lang.HenBatchPerformanceEx]).UnsuccessfulValidations);

            report.HenBatchPerformanceId = performance.Id;
            foreach (SampleCageReport scrDb in warehouseReportsList)
            {
                // Set values from memory sample cage report to db tracked sample cage report.
                SampleCageReportClaims claims = new SampleCageReportClaims(scrDb);
                decimal avgFemaleBirdWeight = scrDb.AvgFemaleBirdWeight;
                decimal avgMaleBirdWeight = scrDb.AvgMaleBirdWeight;
                report.CopyTo(scrDb);
                claims.SetClaimsTo(scrDb);
                scrDb.AvgFemaleBirdWeight = avgFemaleBirdWeight;
                scrDb.AvgMaleBirdWeight = avgMaleBirdWeight;
                scrDb.HenWarehouseAvgFemaleBirdWeight = report.HenWarehouseAvgFemaleBirdWeight;
                scrDb.HenWarehouseAvgMaleBirdWeight = report.HenWarehouseAvgMaleBirdWeight;
                scrDb.HenWarehouseUniformityFemale = report.HenWarehouseUniformityFemale;
                scrDb.HenWarehouseUniformityMale = report.HenWarehouseUniformityMale;
                scrDb.HenWarehouseVariationCoefficientFemale = report.HenWarehouseVariationCoefficientFemale;
                scrDb.HenWarehouseVariationCoefficientMale = report.HenWarehouseVariationCoefficientMale;
                // Update  sample cage report.
                await sampleCageReportService.UpdateAsync(scrDb);
            }
        }

        /// <summary>
        /// actually creates the report and ad the values to the corresponding hen batch performance
        /// </summary>
        private async Task CreateSampleCageReport(SampleCageReport sampleCageReport)
        {
            // Allow backdated entries - removed validation that prevents reports before the last report date
            // DateTime lastReportDate = sampleCageReportService
            //        .GetAll().Where(r => r.HenBatchId == sampleCageReport.HenBatchId)
            //        .OrderByDescending(r => r.Date)
            //        .Select(hr => hr.Date.Date)
            //        .FirstOrDefault();
            // if (lastReportDate != default && lastReportDate > sampleCageReport.Date)
            //     throw new ValidationException(null, new BusinessValidationResult<SampleCageReport>(localizer[Lang.LastReportEx]).UnsuccessfulValidations);

            int week = henBatchService.GetCurrentWeekNumberForDate(sampleCageReport.HenBatchId, sampleCageReport.Date);
            HenBatchPerformance performance = await henBatchPerformanceService.GetAll().FirstOrDefaultAsync(hbp => hbp.HenBatchId == sampleCageReport.HenBatchId && hbp.WeekNumber == week);
            if (performance is null)
                throw new ValidationException(null, new BusinessValidationResult<SampleCageReport>(localizer[Lang.HenBatchPerformanceEx]).UnsuccessfulValidations);

            sampleCageReport.HenBatchPerformanceId = performance.Id;
            List<SampleCageMeasurement> newMeasurement = sampleCageReport.SampleCageMeasurement;
            sampleCageReport.SampleCageMeasurement = null; // To prevent EF from creating them automatically.
            // set sector, farm and company id for filtering
            HenBatch henBatch = henBatchService.Get(sampleCageReport.HenBatchId);
            sampleCageReport.CompanyId = henBatch.CompanyId;
            sampleCageReport.FarmId = henBatch.FarmId;
            sampleCageReport.SectorId = henBatch.SectorId;
            // Create sample cage report.
            await sampleCageReportService.CreateAsync(sampleCageReport);

            // Set sample cage measurements
            if (newMeasurement != null && newMeasurement.Count > 0)
            {
                foreach (SampleCageMeasurement measurement in newMeasurement)
                {
                    measurement.SampleCageReportId = sampleCageReport.Id;
                    await sampleCageMeasurementService.CreateAsync(measurement);
                }
            }

            //Add avg bird and egg weight to hen batch report.
            List<SampleCageReport> allReports = sampleCageReportService.GetAll().Where(scr => scr.HenBatchPerformanceId == performance.Id).ToList();
            IEnumerable<SampleCageReport> femaleBirds = allReports.Where(scr => scr.AvgFemaleBirdWeight > 0);
            IEnumerable<SampleCageReport> maleBirds = allReports.Where(scr => scr.AvgMaleBirdWeight > 0);
            IEnumerable<SampleCageReport> eggs = allReports.Where(scr => scr.AvgEggWeight > 0);
            IEnumerable<SampleCageReport> warehouseAvgFemale = allReports.Where(scr => scr.HenWarehouseAvgFemaleBirdWeight > 0);
            IEnumerable<SampleCageReport> warehouseAvgMale = allReports.Where(scr => scr.HenWarehouseAvgMaleBirdWeight > 0);

            performance.AvgFemaleBirdWeight = femaleBirds.Any() ? femaleBirds.Average(scr => scr.AvgFemaleBirdWeight) : performance.AvgFemaleBirdWeight;
            performance.AvgMaleBirdWeight = maleBirds.Any() ? maleBirds.Average(scr => scr.AvgMaleBirdWeight) : performance.AvgMaleBirdWeight;
            performance.AvgWeightEggFromSCR = eggs.Any() ? eggs.Average(scr => scr.AvgEggWeight) * 1000 : performance.AvgWeightEgg;
            performance.HenWarehouseAvgFemaleBirdWeight = warehouseAvgFemale.Any() ? warehouseAvgFemale.Average(scr => scr.HenWarehouseAvgFemaleBirdWeight) : performance.HenWarehouseAvgFemaleBirdWeight;
            performance.HenWarehouseAvgMaleBirdWeight = warehouseAvgMale.Any() ? warehouseAvgMale.Average(scr => scr.HenWarehouseAvgMaleBirdWeight) : performance.HenWarehouseAvgMaleBirdWeight;
            performance.LastSampleCageReportDate = performance.LastSampleCageReportDate.HasValue && sampleCageReport.Date.Date < performance.LastSampleCageReportDate.Value.Date
                ? performance.LastSampleCageReportDate
                : sampleCageReport.Date;
            performance.AvgVariationCoefficientFemale = allReports.Any(r => r.AvgVariationCoefficientFemale > 0)
                ? allReports.Sum(r => r.AvgVariationCoefficientFemale) / allReports.Where(r => r.AvgVariationCoefficientFemale > 0).Count()
                : 0;
            performance.AvgVariationCoefficientMale = allReports.Any(r => r.AvgVariationCoefficientMale > 0)
                ? allReports.Sum(r => r.AvgVariationCoefficientMale) / allReports.Where(r => r.AvgVariationCoefficientMale > 0).Count()
                : 0;
            performance.AvgUniformityFemale = allReports.Any(r => r.AvgUniformityFemale > 0)
                ? allReports.Sum(r => r.AvgUniformityFemale) / allReports.Where(r => r.AvgUniformityFemale > 0).Count()
                : 0;
            performance.AvgUniformityMale = allReports.Any(r => r.AvgUniformityMale > 0)
                ? allReports.Sum(r => r.AvgUniformityMale) / allReports.Where(r => r.AvgUniformityMale > 0).Count()
                : 0;
            // If avgEggsWeight or AvgWeightBird has changed -> we go to the Database
            if (eggs.Any() || femaleBirds.Any() || maleBirds.Any())
            {
                await henBatchPerformanceService.UpdateAsync(performance);
                if (performance.ParentId.HasValue)
                {
                    List<HenBatchPerformance> relatedPerformances = await henBatchPerformanceService.GetAll()
                        .Where(hbp => hbp.HenBatch.ParentId == henBatch.ParentId && hbp.WeekNumber == week).ToListAsync();

                    HenBatchPerformance performanceParent = await henBatchPerformanceService.GetAll()
                        .FirstOrDefaultAsync(hbp => hbp.Id == performance.ParentId && hbp.WeekNumber == week);

                    performanceParent.AvgVariationCoefficientFemale = relatedPerformances.Any(hbp => hbp.AvgVariationCoefficientFemale != 0) ? relatedPerformances.Where(hbp => hbp.AvgVariationCoefficientFemale != 0).Average(hbp => hbp.AvgVariationCoefficientFemale) : 0;
                    performanceParent.AvgVariationCoefficientMale = relatedPerformances.Any(hbp => hbp.AvgVariationCoefficientMale != 0) ? relatedPerformances.Where(hbp => hbp.AvgVariationCoefficientMale != 0).Average(hbp => hbp.AvgVariationCoefficientMale) : 0;
                    performanceParent.AvgUniformityFemale = relatedPerformances.Any(hbp => hbp.AvgUniformityFemale != 0) ? relatedPerformances.Where(hbp => hbp.AvgUniformityFemale != 0).Average(hbp => hbp.AvgUniformityFemale) : 0;
                    performanceParent.AvgUniformityMale = relatedPerformances.Any(hbp => hbp.AvgUniformityMale != 0) ? relatedPerformances.Where(hbp => hbp.AvgUniformityMale != 0).Average(hbp => hbp.AvgUniformityMale) : 0;

                    performanceParent.AvgFemaleBirdWeight = relatedPerformances.Any(sc => sc.AvgFemaleBirdWeight != 0) ? relatedPerformances.Where(sc => sc.AvgFemaleBirdWeight != 0).Average(sc => sc.AvgFemaleBirdWeight) : null;
                    performanceParent.AvgMaleBirdWeight = relatedPerformances.Any(sc => sc.AvgMaleBirdWeight != 0) ? relatedPerformances.Where(sc => sc.AvgMaleBirdWeight != 0).Average(sc => sc.AvgMaleBirdWeight) : null;
                    performanceParent.HenWarehouseAvgFemaleBirdWeight = relatedPerformances.Any(sc => sc.HenWarehouseAvgFemaleBirdWeight != 0) ? relatedPerformances.Where(sc => sc.HenWarehouseAvgFemaleBirdWeight != 0).Average(sc => sc.HenWarehouseAvgFemaleBirdWeight) : null;
                    performanceParent.HenWarehouseAvgMaleBirdWeight = relatedPerformances.Any(sc => sc.HenWarehouseAvgMaleBirdWeight != 0) ? relatedPerformances.Where(sc => sc.HenWarehouseAvgMaleBirdWeight != 0).Average(sc => sc.HenWarehouseAvgMaleBirdWeight) : null;

                    performanceParent.AvgWeightEggFromSCR = relatedPerformances.Any(hbp => hbp.AvgWeightEggFromSCR != 0) ? relatedPerformances.Where(hbp => hbp.AvgWeightEggFromSCR != 0).Average(hbp => hbp.AvgWeightEggFromSCR) : null;

                    performanceParent.LastSampleCageReportDate = relatedPerformances.Max(hbp => hbp.LastSampleCageReportDate);

                    await henBatchPerformanceService.UpdateAsync(performanceParent);
                }
            }

        }

        public async Task<List<SampleCageReport>> CreateReportsFromTableAsync(CreateSampleCageReportFromTableDTO dto)
        {
            if (dto == null || dto.Reports == null || !dto.Reports.Any())
            {
                throw new ValidationException(localizer["No reports provided"]);
            }

            var measurements = dto.Reports.Select(r => new SampleCageMeasurementDTO
            {
                SampleCageId = r.SampleCageId,
                HenBatchId = r.HenBatchId,
                AvgFemaleBirdWeight = r.WeightFemale ?? 0,
                AvgMaleBirdWeight = r.WeightMale ?? 0,
                VariationCoefficientFemale = r.CvFemale,
                VariationCoefficientMale = r.CvMale,
                UniformityFemale = r.UniformityFemale,
                UniformityMale = r.UniformityMale,
                HasFemaleHen = r.WeightFemale.HasValue && r.WeightFemale.Value > 0,
                HasMaleHen = r.WeightMale.HasValue && r.WeightMale.Value > 0
            }).ToList();

            // Get all sample cage IDs from the measurements
            var sampleCageIds = measurements.Select(m => m.SampleCageId).ToList();

            // Fetch cage info from the database
            var cageInfos = await sampleCageService
                .GetAll(asNoTracking: true)
                .Include(sc => sc.HenBatch).ThenInclude(hb => hb.Line).ThenInclude(l => l.Warehouse)
                .Where(sc => sampleCageIds.Contains(sc.Id))
                .Select(sc => new
                {
                    sc.Id,
                    sc.HenBatchId,
                    WarehouseId = sc.HenBatch.Line.WarehouseId,
                    WarehouseName = sc.HenBatch.Line.Warehouse.Name,
                    LineName = sc.HenBatch.Line.Name
                })
                .ToListAsync();

            // Validate that we found all the sample cages
            if (cageInfos.Count != sampleCageIds.Count)
            {
                var missingIds = sampleCageIds.Except(cageInfos.Select(ci => ci.Id)).ToList();
                throw new ValidationException(localizer[$"Could not find sample cages with IDs: {string.Join(", ", missingIds)}"]);
            }

            // Check for existing reports for this date and batch
            DateTime reportDate = DateTime.Parse(dto.ReportDate);
            var henBatchId = dto.Reports.First().HenBatchId;

            // Allow backdated entries - removed validation that prevents reports before the last report date
            // DateTime lastReportDate = sampleCageReportService
            //        .GetAll().Where(r => r.HenBatchId == henBatchId)
            //        .OrderByDescending(r => r.Date)
            //        .Select(hr => hr.Date.Date)
            //        .FirstOrDefault();
            // if (lastReportDate != default && lastReportDate > reportDate)
            //     throw new ValidationException(null, new BusinessValidationResult<SampleCageReport>(localizer[Lang.LastReportEx]).UnsuccessfulValidations);

            // Validate HenBatchPerformance exists for the week
            int week = henBatchService.GetCurrentWeekNumberForDate(henBatchId, reportDate);
            HenBatchPerformance performance = await henBatchPerformanceService.GetAll().FirstOrDefaultAsync(hbp => hbp.HenBatchId == henBatchId && hbp.WeekNumber == week);
            if (performance is null)
                throw new ValidationException(null, new BusinessValidationResult<SampleCageReport>(localizer[Lang.HenBatchPerformanceEx]).UnsuccessfulValidations);

            // Allow backdated entries - removed validation that requires hen reports to be before sample cage reports
            // var henBatch = await henBatchService.GetAll().Include(hb => hb.Farm).FirstOrDefaultAsync(hb => hb.Id == henBatchId);
            // if (henBatch?.HenStage == HenStage.Laying)
            // {
            //     // Get the last hen report date for this batch
            //     DateTime? lastHenReportDate = await henReportService.GetAll()
            //         .Where(hr => hr.HenBatchId == henBatchId)
            //         .OrderByDescending(hr => hr.Date)
            //         .Select(hr => hr.Date.Date)
            //         .FirstOrDefaultAsync();
            //
            //     if (!lastHenReportDate.HasValue || lastHenReportDate.Value >= reportDate.Date)
            //     {
            //         throw new ValidationException(localizer[Lang.LastReportEx]);
            //     }
            // }

            // Validate date gap
            DateTime? henBatchStart = henBatchService.GetBirdsFirstLoadingDate(henBatchId);
            if (henBatchStart.HasValue && henBatchStart > reportDate)
                throw new ValidationException(localizer[Lang.LastReportEx]);

            // Allow backdated entries - removed validation that prevents date gaps > 1 day
            // if (henBatchStart.HasValue && henBatchStart.Value.Date != reportDate.Date.AddDays(-1) && henBatchStart.Value.Date != reportDate.Date)
            // {
            //     var existingReportsQuery = sampleCageReportService.GetAll()
            //         .Where(r => r.HenBatchId == henBatchId);
            //
            //     if (existingReportsQuery.Any())
            //     {
            //         DateTime lastExistingReportDate = existingReportsQuery
            //                .OrderByDescending(r => r.Date)
            //                .Select(r => r.Date)
            //                .FirstOrDefault();
            //
            //         if ((reportDate.Date - lastExistingReportDate.Date).TotalDays > 1)
            //             throw new ValidationException(localizer["Não é possível criar um relatório com mais de 1 dia desde o último relatório."]);
            //     }
            // }

            // Get all batch IDs (parent and children)
            var batchIds = henBatchService
                .GetAll()
                .Where(hb => hb.Id == henBatchId || hb.ParentId == henBatchId)
                .Select(hb => hb.Id)
                .ToList();

            // Get existing reports for this date
            var existingReports = sampleCageReportService.GetAll()
                .Where(scr => batchIds.Contains(scr.HenBatchId) && scr.Date.Date == reportDate.Date)
                .Include(scr => scr.HenBatch).ThenInclude(hb => hb.Line)
                .Include(scr => scr.SampleCageMeasurement)
                .ToList();

            // Create a dictionary of existing reports by sample cage ID
            var existingReportsBySampleCageId = new Dictionary<Guid, SampleCageReport>();
            foreach (var report in existingReports)
            {
                var sampleCageId = report.CagesRelation?.FirstOrDefault()?.SampleCageId;
                if (sampleCageId.HasValue)
                {
                    existingReportsBySampleCageId[sampleCageId.Value] = report;
                }
            }

            // Group measurements by warehouse safely
            var newEntities = new List<SampleCageReport>();
            var updatedEntities = new List<SampleCageReport>();

            foreach (var itemDto in dto.Reports)
            {
                // Skip if not modified and already exists
                if (!itemDto.IsModified && existingReportsBySampleCageId.ContainsKey(itemDto.SampleCageId))
                {
                    continue;
                }

                // Find the cage info for this report
                var info = cageInfos.FirstOrDefault(ci => ci.Id == itemDto.SampleCageId);

                if (info == null)
                {
                    throw new ValidationException(localizer[$"Could not find sample cage with ID: {itemDto.SampleCageId}"]);
                }

                // Get warehouse averages from the DTO if available
                decimal? warehouseAvgFemaleBirdWeight = null;
                decimal? warehouseAvgMaleBirdWeight = null;
                decimal? warehouseVariationCoefficientFemale = null;
                decimal? warehouseVariationCoefficientMale = null;
                decimal? warehouseUniformityFemale = null;
                decimal? warehouseUniformityMale = null;
                decimal? warehouseEggWeight = null;

                if (itemDto.WarehouseAverages != null)
                {
                    warehouseAvgFemaleBirdWeight = itemDto.WarehouseAverages.WeightF;
                    warehouseAvgMaleBirdWeight = itemDto.WarehouseAverages.WeightM;
                    warehouseVariationCoefficientFemale = itemDto.WarehouseAverages.CvF;
                    warehouseVariationCoefficientMale = itemDto.WarehouseAverages.CvM;
                    warehouseUniformityFemale = itemDto.WarehouseAverages.UnifF;
                    warehouseUniformityMale = itemDto.WarehouseAverages.UnifM;
                    warehouseEggWeight = itemDto.WarehouseAverages.EggWeight;
                }

                var req = new SampleCageReportReq
                {
                    HenBatchId = itemDto.HenBatchId,
                    WarehouseId = itemDto.WarehouseId,
                    Date = reportDate,
                    HenWarehouseAvgFemaleBirdWeight = warehouseAvgFemaleBirdWeight,
                    HenWarehouseAvgMaleBirdWeight = warehouseAvgMaleBirdWeight,
                    HenWarehouseVariationCoefficientFemale = warehouseVariationCoefficientFemale,
                    HenWarehouseVariationCoefficientMale = warehouseVariationCoefficientMale,
                    HenWarehouseUniformityFemale = warehouseUniformityFemale,
                    HenWarehouseUniformityMale = warehouseUniformityMale,
                    HenWarehouseEggWeight = warehouseEggWeight,
                    SampleCageMeasurements = new List<SampleCageMeasurementDTO> {
                    new SampleCageMeasurementDTO {
                    HenBatchId              = itemDto.HenBatchId,
                    SampleCageId            = itemDto.SampleCageId,
                    AvgFemaleBirdWeight     = itemDto.WeightFemale ?? 0,
                    AvgMaleBirdWeight       = itemDto.WeightMale ?? 0,
                    VariationCoefficientFemale = itemDto.CvFemale,
                    VariationCoefficientMale   = itemDto.CvMale,
                    UniformityFemale        = itemDto.UniformityFemale,
                    UniformityMale          = itemDto.UniformityMale,
                    HasFemaleHen            = itemDto.WeightFemale.HasValue && itemDto.WeightFemale.Value > 0,
                    HasMaleHen              = itemDto.WeightMale.HasValue && itemDto.WeightMale.Value > 0
                    }
                  }
                };

                var validatedReports = ValidateDTO(req);
                if (validatedReports == null || !validatedReports.Any())
                {
                    throw new ValidationException(localizer["Failed to validate report"]);
                }

                var report = validatedReports.First();
                report.Name = $"{info.WarehouseName} | {info.LineName}";

                // Set sector, farm and company id for filtering
                var currentHenBatch = henBatchService.Get(itemDto.HenBatchId);
                report.CompanyId = currentHenBatch.CompanyId;
                report.FarmId = currentHenBatch.FarmId;
                report.SectorId = currentHenBatch.SectorId;

                // Add egg weight if provided
                if (itemDto.EggWeight.HasValue && itemDto.EggWeight.Value > 0)
                {
                    report.AvgEggWeight = itemDto.EggWeight.Value / 1000; // Convert from g to kg
                }

                // Ensure warehouse averages are set correctly
                if (itemDto.WarehouseAverages != null)
                {
                    // Make sure these values are set directly on the report
                    if (itemDto.WarehouseAverages.WeightF.HasValue && itemDto.WarehouseAverages.WeightF.Value > 0)
                    {
                        report.HenWarehouseAvgFemaleBirdWeight = itemDto.WarehouseAverages.WeightF.Value / 1000; // Convert from g to kg
                    }

                    if (itemDto.WarehouseAverages.WeightM.HasValue && itemDto.WarehouseAverages.WeightM.Value > 0)
                    {
                        report.HenWarehouseAvgMaleBirdWeight = itemDto.WarehouseAverages.WeightM.Value / 1000; // Convert from g to kg
                    }

                    if (itemDto.WarehouseAverages.CvF.HasValue)
                    {
                        report.HenWarehouseVariationCoefficientFemale = itemDto.WarehouseAverages.CvF.Value;
                    }

                    if (itemDto.WarehouseAverages.CvM.HasValue)
                    {
                        report.HenWarehouseVariationCoefficientMale = itemDto.WarehouseAverages.CvM.Value;
                    }

                    if (itemDto.WarehouseAverages.UnifF.HasValue)
                    {
                        report.HenWarehouseUniformityFemale = itemDto.WarehouseAverages.UnifF.Value;
                    }

                    if (itemDto.WarehouseAverages.UnifM.HasValue)
                    {
                        report.HenWarehouseUniformityMale = itemDto.WarehouseAverages.UnifM.Value;
                    }

                    if (itemDto.WarehouseAverages.EggWeight.HasValue && itemDto.WarehouseAverages.EggWeight.Value > 0)
                    {
                        report.AvgEggWeight = itemDto.WarehouseAverages.EggWeight.Value / 1000; // Convert from g to kg
                    }
                }

                // Check if this is an update to an existing report
                if (itemDto.ReportId.HasValue && existingReportsBySampleCageId.ContainsKey(itemDto.SampleCageId))
                {
                    var existingReport = existingReportsBySampleCageId[itemDto.SampleCageId];
                    report.Id = existingReport.Id;
                    updatedEntities.Add(report);
                }
                else
                {
                    newEntities.Add(report);
                }
            }

            // Create new reports
            if (newEntities.Any())
            {
                await CreateReports(newEntities);
            }

            // Update existing reports
            foreach (var report in updatedEntities)
            {
                await UpdateAndAddToHenBatchPerformanceAsync(report);
            }

            // Return all entities (new and updated)
            return newEntities.Concat(updatedEntities).ToList();
        }

        public List<SampleCageReportHenBatchDTO> GetActiveHenBatchesByFarm(Guid farmId, HenStage henStage)
        {
            var query = from hb in henBatchService.GetAll()
                        join perf in henBatchPerformanceService.GetAll()
                            on hb.Id equals perf.HenBatchId
                        where hb.Active
                           && hb.Line.Warehouse.FarmId == farmId
                           && hb.HenStage == henStage
                        let parentId = hb.ParentId ?? hb.Id
                        let parentCode = hb.Parent != null ? hb.Parent.Code : hb.Code
                        select new
                        {
                            ParentId = parentId,
                            ParentCode = parentCode
                        };

            var distinctParents = query
                .Distinct()
                .Select(x => new SampleCageReportHenBatchDTO
                {
                    Id = x.ParentId,
                    Code = x.ParentCode
                })
                .ToList();

            return distinctParents;
        }


        /// <summary>
        /// Sample cage report UpdateAsync override.
        /// Updates the sample cage report and all its dependant relationships.
        /// </summary>
        public async Task UpdateAndAddToHenBatchPerformanceAsync(SampleCageReport sampleCageReport)
        {
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                // Get current sample cage report from db.
                // Only include relationships that need to be auto-updated by EF Core.
                SampleCageReport dbSampleCageReport = await sampleCageReportService.GetAll(true)
                .Include(sc => sc.SampleCageMeasurement)
                .FirstOrDefaultAsync(scr => scr.Id == sampleCageReport.Id);

                // Update sample cage measurement using the sample cage measurement service.
                List<SampleCageMeasurement> measurementsDB = dbSampleCageReport.SampleCageMeasurement;
                List<SampleCageReportClaims> reportClaims = new List<SampleCageReportClaims>();
                foreach (SampleCageMeasurement measurement in sampleCageReport.SampleCageMeasurement)
                {
                    SampleCageMeasurement measurementDB = measurementsDB.FirstOrDefault(m => m.Id == measurement.Id);
                    reportClaims.Add(new SampleCageReportClaims(measurementDB.SampleCageReport));
                    measurement.CopyTo(measurementDB);
                    await sampleCageMeasurementService.UpdateAsync(measurement);
                }

                // Set values from memory sample cage report to db tracked sample cage report.
                sampleCageReport.CopyTo(dbSampleCageReport);

                foreach (SampleCageMeasurement measurement in dbSampleCageReport.SampleCageMeasurement)
                {
                    SampleCageReportClaims claims = reportClaims.FirstOrDefault(m => m.ReportId == measurement.SampleCageReportId);
                    claims.SetClaimsTo(measurement.SampleCageReport);
                }
                // Update  sample cage report.
                await sampleCageReportService.UpdateAsync(dbSampleCageReport);

                HenBatchPerformance performance = henBatchPerformanceService.Get(sampleCageReport.HenBatchPerformanceId.Value);
                //Update avg bird and egg weight to hen batch report
                List<SampleCageReport> allReports = sampleCageReportService.GetAll().Where(scr => scr.HenBatchPerformanceId == performance.Id).ToList();

                IEnumerable<SampleCageReport> femaleBirds = allReports.Where(scr => scr.AvgFemaleBirdWeight > 0);
                IEnumerable<SampleCageReport> maleBirds = allReports.Where(scr => scr.AvgMaleBirdWeight > 0);
                IEnumerable<SampleCageReport> eggs = allReports.Where(scr => scr.AvgEggWeight > 0);
                IEnumerable<SampleCageReport> warehouseAvgFemale = allReports.Where(scr => scr.HenWarehouseAvgFemaleBirdWeight > 0);
                IEnumerable<SampleCageReport> warehouseAvgMale = allReports.Where(scr => scr.HenWarehouseAvgMaleBirdWeight > 0);

                performance.AvgFemaleBirdWeight = femaleBirds.Any() ? femaleBirds.Average(scr => scr.AvgFemaleBirdWeight) : performance.AvgFemaleBirdWeight;
                performance.AvgMaleBirdWeight = maleBirds.Any() ? maleBirds.Average(scr => scr.AvgMaleBirdWeight) : performance.AvgMaleBirdWeight;
                performance.AvgWeightEggFromSCR = eggs.Any() ? eggs.Average(scr => scr.AvgEggWeight) * 1000 : performance.AvgWeightEgg;
                performance.HenWarehouseAvgFemaleBirdWeight = warehouseAvgFemale.Any() ? warehouseAvgFemale.Average(scr => scr.HenWarehouseAvgFemaleBirdWeight) : performance.HenWarehouseAvgFemaleBirdWeight;
                performance.HenWarehouseAvgMaleBirdWeight = warehouseAvgMale.Any() ? warehouseAvgMale.Average(scr => scr.HenWarehouseAvgMaleBirdWeight) : performance.HenWarehouseAvgMaleBirdWeight;

                await henBatchPerformanceService.UpdateAsync(performance);
            });
        }

        /// <summary>
        /// Returns all sample cage reports filtered including its hen batch with its line with its warehouse and its genetic.
        /// </summary>
        public IQueryable<SampleCageReportDTO> GetFullFiltered(Dictionary<string, string> filters)
        {
            string filterValue = filters["henStage"];
            IQueryable<SampleCageReport> sampleCageReports;
            if (string.IsNullOrEmpty(filterValue))
            {
                sampleCageReports = sampleCageReportService.GetAllFull();
            }
            else
            {
                HenStage henStage = EnumHelper<HenStage>.Parse(filterValue);
                sampleCageReports = sampleCageReportService.GetAllFull(henStage);
            }

            if (!string.IsNullOrEmpty(filters["henBatchStatus"]))
            {
                switch (filters["henBatchStatus"])
                {
                    case "open":
                        sampleCageReports = sampleCageReports.Where(sc => sc.HenBatchPerformance.HenBatch.DateEnd == null);
                        break;
                    case "closed":
                        sampleCageReports = sampleCageReports.Where(sc => sc.HenBatchPerformance.HenBatch.DateEnd != null);
                        break;
                    default:
                        break;
                }
            }

            bool hasHenBatchCategories = !string.IsNullOrEmpty(filters["hasHenBatchCategories"]) && bool.Parse(filters["hasHenBatchCategories"]);

            IQueryable<SampleCageReportDTO> sampleCageReportsDTO = sampleCageReports.Select(scr => new SampleCageReportDTO
            {
                Id = scr.Id,
                Name = scr.Name,
                Date = scr.Date,
                FarmCode = scr.HenBatchPerformance.HenBatch.LineId.HasValue
                           ? scr.HenBatchPerformance.HenBatch.Line.Farm.Code
                           : scr.HenBatchPerformance.HenBatch.Farm.Code,
                FarmName = scr.HenBatchPerformance.HenBatch.LineId.HasValue
                        ? scr.HenBatchPerformance.HenBatch.Line.Farm.Name
                        : scr.HenBatchPerformance.HenBatch.Farm.Name,
                Warehouse = scr.HenBatchPerformance.HenBatch.LineId.HasValue
                        ? scr.HenBatchPerformance.HenBatch.Line.Warehouse.Name
                        : " ",
                Line = scr.HenBatchPerformance.HenBatch.LineId.HasValue
                        ? scr.HenBatchPerformance.HenBatch.Line.Name
                        : " ",
                HenBatch = scr.HenBatchPerformance.HenBatch.Code,
                Genetic = scr.HenBatchPerformance.Genetic.Name,
                AvgFemaleBirdWeight = scr.AvgFemaleBirdWeight * 1000,
                AvgMaleBirdWeight = scr.AvgMaleBirdWeight * 1000,
                UniformityFemale = scr.SampleCageMeasurement.Any(c => c.UniformityFemale > 0) ? scr.SampleCageMeasurement.Where(c => c.UniformityFemale > 0).Sum(c => c.UniformityFemale) / (decimal)scr.SampleCageMeasurement.Count(c => c.UniformityFemale > 0) : (decimal?)null,
                UniformityMale = scr.SampleCageMeasurement.Any(c => c.UniformityMale > 0) ? scr.SampleCageMeasurement.Where(c => c.UniformityMale > 0).Sum(c => c.UniformityMale) / (decimal)scr.SampleCageMeasurement.Count(c => c.UniformityMale > 0) : (decimal?)null,
                VariationCoefficientFemale = scr.SampleCageMeasurement.Any(c => c.VariationCoefficientFemale > 0) ? scr.SampleCageMeasurement.Where(c => c.VariationCoefficientFemale > 0).Sum(c => c.VariationCoefficientFemale) / (decimal)scr.SampleCageMeasurement.Count(c => c.VariationCoefficientFemale > 0) : (decimal?)null,
                VariationCoefficientMale = scr.SampleCageMeasurement.Any(c => c.VariationCoefficientMale > 0) ? scr.SampleCageMeasurement.Where(c => c.VariationCoefficientMale > 0).Sum(c => c.VariationCoefficientMale) / (decimal)scr.SampleCageMeasurement.Count(c => c.VariationCoefficientMale > 0) : (decimal?)null,
                HenBatchCategory = hasHenBatchCategories && scr.HenBatchPerformance.HenBatch.Category != null ? scr.HenBatchPerformance.HenBatch.Category.Name : null,
                HenWarehouseAvgFemaleBirdWeight = scr.HenWarehouseAvgFemaleBirdWeight * 1000,
                HenWarehouseAvgMaleBirdWeight = scr.HenWarehouseAvgMaleBirdWeight * 1000,
                HenWarehouseUniformityFemale = scr.HenWarehouseUniformityFemale,
                HenWarehouseUniformityMale = scr.HenWarehouseUniformityMale,
                HenWarehouseVariationCoefficientFemale = scr.HenWarehouseVariationCoefficientFemale,
                HenWarehouseVariationCoefficientMale = scr.HenWarehouseVariationCoefficientMale,

            });

            return sampleCageReportsDTO;
        }

        /// <summary>
        /// Deletes a sample cage report, removes it from the HenBatchPerformance,
        /// and updates the values from the HenBatchPerformance
        /// </summary>
        public async Task DeleteAndRemoveFromHenBatchPerformanceAsync(Guid id)
        {
            SampleCageReport sampleCageReport = sampleCageReportService.GetFull(id);

            HenBatchPerformance performance = await henBatchPerformanceService.GetAll()
                .FirstAsync(p => p.Id == sampleCageReport.HenBatchPerformanceId.Value);

            performance.SampleCageReports.Remove(sampleCageReport);

            if (performance.SampleCageReports.Any())
            {
                //Update avg bird and egg weight to hen batch report
                List<SampleCageReport> allReports = sampleCageReportService.GetAll().Where(scr => scr.HenBatchPerformanceId == performance.Id).ToList();

                performance.AvgFemaleBirdWeight = allReports.Where(scr => scr.AvgFemaleBirdWeight > 0).Average(scr => scr.AvgFemaleBirdWeight);
                performance.AvgMaleBirdWeight = allReports.Where(scr => scr.AvgMaleBirdWeight > 0).Average(scr => scr.AvgMaleBirdWeight);
                performance.AvgWeightEggFromSCR = allReports.Where(scr => scr.AvgEggWeight > 0).Average(scr => scr.AvgEggWeight) * 1000;

                await henBatchPerformanceService.UpdateAsync(performance);
            }

            await sampleCageReportService.DeleteAsync(sampleCageReport);

        }

        /// <summary>
        /// Validate DTO of sample cage report create from warehouse endpoint and returns entity
        /// </summary>
        public List<SampleCageReport> ValidateDTO(SampleCageReportReq sampleCageReportReq)
        {
            // Check if we have either warehouse averages or individual box values
            bool hasWarehouseAverages =
                (sampleCageReportReq.HenWarehouseAvgFemaleBirdWeight.HasValue && sampleCageReportReq.HenWarehouseAvgFemaleBirdWeight.Value > 0) ||
                (sampleCageReportReq.HenWarehouseAvgMaleBirdWeight.HasValue && sampleCageReportReq.HenWarehouseAvgMaleBirdWeight.Value > 0);

            bool hasIndividualValues = sampleCageReportReq.SampleCageMeasurements.Any(scm =>
                scm.AvgFemaleBirdWeight > 0 || scm.AvgMaleBirdWeight > 0);

            if (!hasWarehouseAverages && !hasIndividualValues)
            {
                throw new ValidationException(localizer[Lang.WeightRequired]);
            }

            var validWarehouseIds = henBatchService
             .GetAll()
             .Where(hb =>
                    hb.Id == sampleCageReportReq.HenBatchId ||
                    hb.ParentId == sampleCageReportReq.HenBatchId ||
                    sampleCageReportReq.SampleCageMeasurements.Select(scm => scm.HenBatchId).Contains(hb.Id))
             .Select(hb => hb.Line.WarehouseId)
             .Distinct()
             .ToList();

            if (!validWarehouseIds.Contains(sampleCageReportReq.WarehouseId))
            {
                throw new ValidationException(
                    null,
                    new Dictionary<string, string> {
                { nameof(sampleCageReportReq.WarehouseId),
                  localizer["O aviário selecionado não corresponde ao lote informado."] }
                    });
            }


            var errors = new Dictionary<string, string>();
            var warehouseErrors = new List<string>();

            if (sampleCageReportReq.HenWarehouseAvgFemaleBirdWeight > 0)
            {
                if (!sampleCageReportReq.HenWarehouseVariationCoefficientFemale.HasValue)
                    warehouseErrors.Add(localizer[Lang.VariationCoefficientFemaleRequired]);
                if (!sampleCageReportReq.HenWarehouseUniformityFemale.HasValue)
                    warehouseErrors.Add(localizer[Lang.UniformityFemaleRequired]);
            }
            if (sampleCageReportReq.HenWarehouseAvgMaleBirdWeight > 0)
            {
                if (!sampleCageReportReq.HenWarehouseVariationCoefficientMale.HasValue)
                    warehouseErrors.Add(localizer[Lang.VariationCoefficientMaleRequired]);
                if (!sampleCageReportReq.HenWarehouseUniformityMale.HasValue)
                    warehouseErrors.Add(localizer[Lang.UniformityMaleRequired]);
            }
            if (warehouseErrors.Any())
                errors.Add("warehouse", string.Join(",", warehouseErrors));

            foreach (var cage in sampleCageReportReq.SampleCageMeasurements)
            {
                var cageErrors = new List<string>();
                if (cage.HasFemaleHen && cage.AvgFemaleBirdWeight > 0)
                {
                    if (!cage.VariationCoefficientFemale.HasValue)
                        cageErrors.Add(localizer[Lang.VariationCoefficientFemaleRequired]);
                    if (!cage.UniformityFemale.HasValue)
                        cageErrors.Add(localizer[Lang.UniformityFemaleRequired]);
                }
                if (cage.HasMaleHen && cage.AvgMaleBirdWeight > 0)
                {
                    if (!cage.VariationCoefficientMale.HasValue)
                        cageErrors.Add(localizer[Lang.VariationCoefficientMaleRequired]);
                    if (!cage.UniformityMale.HasValue)
                        cageErrors.Add(localizer[Lang.UniformityMaleRequired]);
                }
                if (cageErrors.Any())
                    errors.Add(cage.SampleCageId.ToString(), string.Join(",", cageErrors));
            }

            if (errors.Any())
                throw new ValidationException("", errors);

            return sampleCageReportReq.ToEntity(capacityUnitBusinessLogic);
        }

        /// <summary>
        /// Returns a list with all sample cages from specific warehouse to use in mobile app sample cage report.
        /// </summary>
        public async Task<List<SampleCageRes>> GetSampleCages(Guid warehouseId, DateTime date)
        {
            List<HBPerformanceDTO> performances = await henBatchPerformanceService.GetAllFromWarehouse(warehouseId).Select(hbp => new HBPerformanceDTO(hbp)).ToListAsync();

            if (!performances.Any())
                throw new ValidationException(localizer[Lang.HenBatchPerformanceEx]);

            IEnumerable<HenBatch> henBatchesQuery = henBatchService.GetAll(asNoTracking: true)
                .Include(hb => hb.Line)
                .Include(hb => hb.Farm)
                .Where(hb => hb.Line.WarehouseId == warehouseId && (hb.HenAmountFemale > 0 || hb.HenAmountMale > 0) && hb.Active && !hb.DateEnd.HasValue);

            if (!henBatchesQuery.Any())
                throw new ValidationException(localizer[Lang.NoHenBatchesAvailable]);

            List<HenBatch> henBatches = new List<HenBatch>();

            foreach (HenBatch henBatch in henBatchesQuery)
            {
                int weekNumber = henBatchService.GetCurrentWeekNumberForDate(henBatch, date);

                if (performances.Any(hbp => hbp.HenBatchId == henBatch.Id && hbp.WeekNumber == weekNumber))
                    henBatches.Add(henBatch);
            }

            if (!henBatches.Any())
                throw new ValidationException(localizer[Lang.HenBatchPerformanceEx]);

            List<Guid> henBatchIds = henBatches.Select(hb => hb.Id).ToList();

            List<SampleCage> cages = await sampleCageService.GetAll(asNoTracking: true)
                .Where(sc => sc.Active && henBatchIds.Contains(sc.HenBatchId)).ToListAsync();

            List<SampleCageRes> result = cages
                .Select(sc =>
                    new SampleCageRes(
                        sc,
                        henBatches.FirstOrDefault(hb => hb.Id == sc.HenBatchId),
                        performances.Where(hbp => hbp.HenBatchId == sc.HenBatchId).OrderByDescending(hbp => hbp.Date).FirstOrDefault()
                        )
                ).ToList();

            return result;
        }

        /// <summary>
        /// Returns a list with all sample cages for offline functionality.
        /// </summary>
        public async Task<List<SampleCageFullRes>> GetSampleCagesForOffline()
        {
            List<SampleCageFullRes> cages = await sampleCageService.GetAll(asNoTracking: true)
                .Where(sc =>
                    (sc.HenBatch.HenAmountFemale > 0 || sc.HenBatch.HenAmountMale > 0) &&
                    sc.Active &&
                    !sc.HenBatch.DateEnd.HasValue &&
                    sc.HenBatch.ParentId.HasValue
                 )
                .Select(sc => new SampleCageFullRes()
                {
                    WarehouseId = sc.HenBatch.Line.WarehouseId,
                    HenBatchPerformanceId = sc.HenBatch.HenBatchPerformances.OrderByDescending(hbp => hbp.Date).FirstOrDefault().Id,
                    HenBatchId = sc.HenBatch.Id,
                    HasFemaleHen = sc.HenBatch.HenAmountFemale > 0,
                    HasMaleHen = sc.HenBatch.HenAmountMale > 0,
                    Name = sc.Name,
                    Id = sc.Id
                })
                .ToListAsync();

            return cages;
        }

        /// <summary>
        /// Get all sample cage reports from the respective farm.
        /// </summary>
        public async Task<SampleCageReportPage> GetListByFarm(Guid farmId, int page, int pageSize)
        {
            IQueryable<SampleCageReportItem> query = sampleCageReportService.GetAll(asNoTracking: true).Where(scr => scr.HenBatch.FarmId.Value == farmId)
                .OrderByDescending(scr => scr.CreatedDate)
                .Select(scr => new SampleCageReportItem()
                {
                    Id = scr.Id,
                    Date = scr.Date,
                    AvgEggWeight = scr.AvgEggWeight * 1000,
                    //implement an action filter to avoid operations with the measures
                    AvgFemaleBirdWeight = scr.AvgFemaleBirdWeight * 1000,
                    //implement an action filter to avoid operations with the measures
                    AvgMaleBirdWeight = scr.AvgMaleBirdWeight * 1000,
                    HenBatchName = $"{scr.HenBatch.Line.Warehouse.Name} | {scr.HenBatch.Line.Name}",
                    VariationCoefficientFemale = scr.AvgVariationCoefficientFemale,
                    VariationCoefficientMale = scr.AvgVariationCoefficientMale,
                    UniformityFemale = scr.AvgUniformityFemale,
                    UniformityMale = scr.AvgUniformityMale,
                }
            );

            if (pageSize > 0)
                query = query.Skip((page - 1) * pageSize).Take(pageSize);

            List<SampleCageReportItem> items = await query.ToListAsync();

            return new SampleCageReportPage()
            {
                Total = items.Count(),
                Page = page,
                PageSize = pageSize > 0 ? pageSize : items.Count(),
                Items = items
            };
        }

        /// <summary>
        /// Get all reports for offline functionality.
        /// </summary>
        public async Task<List<SampleCageReportFullItem>> GetFullListForOffline()
        {
            List<Guid> userFarmIds = await farmService.GetAll(asNoTracking: true).Select(f => f.Id).ToListAsync();

            List<SampleCageReportFullItem> query = await sampleCageReportService.GetAll(asNoTracking: true).Where(scr => scr.HenBatch.FarmId.HasValue && userFarmIds.Contains(scr.HenBatch.FarmId.Value)).Select(scr => new SampleCageReportFullItem
            {
                Id = scr.Id,
                FarmId = scr.HenBatch.FarmId.Value,
                Date = scr.Date,
                AvgFemaleBirdWeight = scr.AvgFemaleBirdWeight,
                AvgMaleBirdWeight = scr.AvgMaleBirdWeight,
                HenBatcName = $"{scr.HenBatch.Line.Warehouse.Name} | {scr.HenBatch.Line.Name}",
                VariationCoefficientFemale = scr.AvgVariationCoefficientFemale,
                VariationCoefficientMale = scr.AvgVariationCoefficientMale,
                UniformityFemale = scr.AvgUniformityFemale,
                UniformityMale = scr.AvgUniformityMale
            }).OrderByDescending(scr => scr.Date).ToListAsync();

            return query;
        }

        private class SampleCageReportClaims
        {
            public Guid ReportId;
            public Guid? CompanyId;
            public Guid? FarmId;
            public Guid? SectorId;

            public SampleCageReportClaims(SampleCageReport sampleCageReport)
            {
                ReportId = sampleCageReport.Id;
                CompanyId = sampleCageReport.CompanyId;
                FarmId = sampleCageReport.FarmId;
                SectorId = sampleCageReport.SectorId;
            }

            public void SetClaimsTo(SampleCageReport sampleCageReport)
            {
                sampleCageReport.CompanyId = this.CompanyId;
                sampleCageReport.FarmId = this.FarmId;
                sampleCageReport.SectorId = this.SectorId;
            }
        }

        /// <summary>
        /// Gets the list of processed warehouse IDs for a specific hen batch and date
        /// </summary>
        public List<Guid> GetProcessedWarehouseIds(Guid henBatchId, DateTime reportDate)
        {
            // Get all sample cage reports for this date and batch
            var reports = sampleCageReportService.GetAll()
                .Where(scr => scr.HenBatchId == henBatchId && scr.Date.Date == reportDate.Date)
                .Include(scr => scr.HenBatch).ThenInclude(hb => hb.Line)
                .ToList();

            // Get all warehouse IDs from the reports
            var warehouseIds = reports
                .Select(scr => scr.HenBatch.Line.WarehouseId)
                .Distinct()
                .ToList();

            return warehouseIds;
        }

        /// <summary>
        /// Saves processed warehouse IDs for a specific hen batch and date
        /// </summary>
        public async Task SaveProcessedWarehouseIds(Guid henBatchId, DateTime reportDate, List<Guid> warehouseIds)
        {
            // This is a no-op since we're now tracking processed warehouses based on the reports themselves
            await Task.CompletedTask;
        }
    }
}